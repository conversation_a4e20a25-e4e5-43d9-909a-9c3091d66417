<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ site.title }}{% endblock %}</title>
    <meta name="description" content="{% block description %}{{ site.description }}{% endblock %}">
    <meta name="author" content="{{ site.author }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: {{ theme.primary_color or '#2563eb' }};
            --secondary-color: {{ theme.secondary_color or '#64748b' }};
            --font-family: {{ theme.font_family or 'Inter, sans-serif' }};
        }
        
        body {
            font-family: var(--font-family);
            line-height: 1.6;
            color: #333;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .paper-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .paper-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .paper-title {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }
        
        .paper-title:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }
        
        .paper-authors {
            color: var(--secondary-color);
            font-size: 0.9rem;
        }
        
        .paper-summary {
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .category-badge {
            background-color: var(--primary-color);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
        }
        
        .category-badge:hover {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .key-points {
            background-color: #f8f9fa;
            border-left: 4px solid var(--primary-color);
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .key-points h6 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .key-points ul {
            margin-bottom: 0;
            padding-left: 1.2rem;
        }
        
        .footer {
            background-color: #f8f9fa;
            color: var(--secondary-color);
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        .search-box {
            max-width: 400px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        {% if theme.dark_mode %}
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
                color: #e5e5e5;
            }
            
            .paper-card {
                background-color: #2d2d2d;
                color: #e5e5e5;
            }
            
            .navbar {
                background-color: #2d2d2d !important;
            }
            
            .footer {
                background-color: #2d2d2d;
            }
        }
        {% endif %}
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>
                {{ site.title }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-tags me-1"></i>分类
                        </a>
                        <ul class="dropdown-menu">
                            {% for category in categories.keys() %}
                            <li><a class="dropdown-item" href="/categories/{{ category }}.html">{{ category }}</a></li>
                            {% endfor %}
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/rss.xml" target="_blank">
                            <i class="fas fa-rss me-1"></i>RSS
                        </a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <div class="d-flex search-box">
                    <input class="form-control me-2" type="search" placeholder="搜索论文..." id="searchInput">
                    <button class="btn btn-outline-primary" type="button" onclick="searchPapers()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>{{ site.title }}</h6>
                    <p class="mb-0">{{ site.description }}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-robot me-1"></i>
                        由AI自动生成 | 
                        <i class="fas fa-clock me-1"></i>
                        更新时间: {{ generated_at[:10] if generated_at else '' }}
                    </p>
                    <p class="mb-0">
                        <a href="https://github.com" target="_blank" class="text-decoration-none">
                            <i class="fab fa-github me-1"></i>GitHub
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 搜索功能
        function searchPapers() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const papers = document.querySelectorAll('.paper-card');
            
            papers.forEach(paper => {
                const title = paper.querySelector('.paper-title').textContent.toLowerCase();
                const summary = paper.querySelector('.paper-summary').textContent.toLowerCase();
                const authors = paper.querySelector('.paper-authors').textContent.toLowerCase();
                
                if (title.includes(query) || summary.includes(query) || authors.includes(query)) {
                    paper.style.display = 'block';
                } else {
                    paper.style.display = 'none';
                }
            });
        }
        
        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchPapers();
            }
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
