# 论文自动化处理系统配置文件
# 复制此文件为 config.yaml 并填入实际配置

# 基础配置
app:
  name: "论文自动化处理系统"
  version: "0.1.0"
  debug: false
  log_level: "INFO"
  timezone: "Asia/Shanghai"

# 论文爬取配置
crawler:
  # 爬取源配置
  sources:
    arxiv:
      enabled: true
      base_url: "http://export.arxiv.org/api/query"
      max_results: 50
      categories:
        - "cs.AI"  # 人工智能
        - "cs.LG"  # 机器学习
        - "cs.CL"  # 计算语言学
        - "cs.CV"  # 计算机视觉
      keywords:
        - "machine learning"
        - "deep learning"
        - "neural network"
        - "artificial intelligence"
    
    pubmed:
      enabled: false
      base_url: "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/"
      api_key: ""  # 可选，提高请求限制
      max_results: 20
      search_terms:
        - "machine learning[Title/Abstract]"
        - "artificial intelligence[Title/Abstract]"
  
  # 爬取策略
  strategy:
    interval_hours: 24  # 爬取间隔（小时）
    max_papers_per_run: 100
    duplicate_check: true
    date_range_days: 7  # 只爬取最近N天的论文
  
  # 过滤规则
  filters:
    min_abstract_length: 100
    exclude_keywords:
      - "survey"
      - "review"
    include_keywords:
      - "novel"
      - "state-of-the-art"
      - "breakthrough"

# AI 摘要生成配置
summarizer:
  # 默认使用的AI服务
  default_provider: "openai"
  
  # OpenAI 配置
  openai:
    api_key: ""  # 从环境变量 OPENAI_API_KEY 读取
    model: "gpt-3.5-turbo"
    max_tokens: 500
    temperature: 0.3
    timeout: 30
  
  # Anthropic Claude 配置
  anthropic:
    api_key: ""  # 从环境变量 ANTHROPIC_API_KEY 读取
    model: "claude-3-sonnet-20240229"
    max_tokens: 500
    temperature: 0.3
    timeout: 30
  
  # 本地模型配置
  local:
    enabled: false
    model_path: "models/summarization-model"
    device: "cpu"  # 或 "cuda"
  
  # 摘要配置
  summary:
    max_length: 300
    min_length: 100
    language: "zh"  # zh: 中文, en: 英文
    style: "academic"  # academic, casual, technical
    include_keywords: true
    include_methodology: true

# 网页渲染配置
renderer:
  # 输出配置
  output:
    directory: "output"
    static_files: "static"
    template_dir: "templates"
  
  # 网站配置
  site:
    title: "最新论文摘要"
    description: "AI自动生成的最新学术论文摘要"
    author: "论文自动化系统"
    url: "https://your-username.github.io/paper-automation"
  
  # 页面配置
  pages:
    papers_per_page: 20
    enable_search: true
    enable_categories: true
    enable_tags: true
    enable_rss: true
  
  # 主题配置
  theme:
    primary_color: "#2563eb"
    secondary_color: "#64748b"
    font_family: "Inter, sans-serif"
    dark_mode: true

# 邮件推送配置
mailer:
  # SMTP 配置
  smtp:
    server: "smtp.gmail.com"  # 或其他SMTP服务器
    port: 587
    use_tls: true
    username: ""  # 发送邮箱
    password: ""  # 从环境变量 EMAIL_PASSWORD 读取
  
  # 邮件内容配置
  email:
    from_name: "论文自动化系统"
    subject_template: "📚 今日论文摘要 - {date}"
    max_papers_per_email: 10
    include_full_abstract: false
    include_pdf_links: true
  
  # 收件人配置
  recipients:
    - email: "<EMAIL>"
      name: "用户1"
      categories: ["cs.AI", "cs.LG"]
    - email: "<EMAIL>"
      name: "用户2"
      categories: ["cs.CV"]
  
  # 发送策略
  schedule:
    enabled: true
    time: "09:00"  # 每天发送时间
    timezone: "Asia/Shanghai"
    skip_weekends: false

# GitHub Actions 配置
github:
  # 仓库配置
  repository:
    owner: "your-username"
    name: "paper-automation"
    branch: "main"
  
  # Pages 配置
  pages:
    enabled: true
    custom_domain: ""  # 可选
  
  # 工作流配置
  workflow:
    schedule: "0 9 * * *"  # 每天9点执行
    timeout_minutes: 30

# 数据存储配置
storage:
  # 数据库配置
  database:
    type: "sqlite"
    path: "data/papers.db"
  
  # 缓存配置
  cache:
    enabled: true
    ttl_hours: 24
    max_size_mb: 100

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/app.log"
  rotation: "1 week"
  retention: "1 month"
