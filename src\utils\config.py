"""
配置管理模块
"""

import os
import yaml
from pathlib import Path
from typing import Any, Dict, Optional
from loguru import logger


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为 config/config.yaml
        """
        self.config_path = config_path or "config/config.yaml"
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            config_file = Path(self.config_path)
            
            if not config_file.exists():
                logger.warning(f"配置文件不存在: {self.config_path}")
                logger.info("尝试从示例配置文件创建默认配置")
                self._create_default_config()
            
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
            
            # 从环境变量覆盖敏感配置
            self._load_env_overrides()
            
            logger.info(f"配置文件加载成功: {self.config_path}")
            
        except Exception as e:
            logger.error(f"配置文件加载失败: {str(e)}")
            raise
    
    def _create_default_config(self):
        """从示例配置创建默认配置"""
        example_config = Path("config/config.example.yaml")
        default_config = Path(self.config_path)
        
        if example_config.exists():
            # 确保目录存在
            default_config.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制示例配置
            with open(example_config, 'r', encoding='utf-8') as src:
                content = src.read()
            
            with open(default_config, 'w', encoding='utf-8') as dst:
                dst.write(content)
            
            logger.info(f"已创建默认配置文件: {self.config_path}")
        else:
            logger.error("示例配置文件不存在，无法创建默认配置")
    
    def _load_env_overrides(self):
        """从环境变量加载敏感配置"""
        env_mappings = {
            'OPENAI_API_KEY': 'summarizer.openai.api_key',
            'ANTHROPIC_API_KEY': 'summarizer.anthropic.api_key',
            'EMAIL_PASSWORD': 'mailer.smtp.password',
            'SMTP_SERVER': 'mailer.smtp.server',
            'SMTP_USERNAME': 'mailer.smtp.username',
            'GITHUB_TOKEN': 'github.token',
        }
        
        for env_key, config_key in env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value:
                self.set(config_key, env_value)
                logger.debug(f"从环境变量加载配置: {config_key}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'crawler.sources.arxiv.enabled'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self.config_data
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception:
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config_data
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置段
        
        Args:
            section: 配置段名称
            
        Returns:
            配置段字典
        """
        return self.get(section, {})
    
    def save_config(self):
        """保存配置到文件"""
        try:
            config_file = Path(self.config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(
                    self.config_data, 
                    f, 
                    default_flow_style=False, 
                    allow_unicode=True,
                    indent=2
                )
            
            logger.info(f"配置文件保存成功: {self.config_path}")
            
        except Exception as e:
            logger.error(f"配置文件保存失败: {str(e)}")
            raise
    
    def validate_config(self) -> bool:
        """
        验证配置的完整性
        
        Returns:
            配置是否有效
        """
        required_keys = [
            'app.name',
            'crawler.sources',
            'summarizer.default_provider',
            'renderer.output.directory',
            'mailer.smtp.server'
        ]
        
        missing_keys = []
        for key in required_keys:
            if self.get(key) is None:
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"配置验证失败，缺少必需的配置项: {missing_keys}")
            return False
        
        logger.info("配置验证通过")
        return True
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return f"ConfigManager(config_path='{self.config_path}')"
    
    def __repr__(self) -> str:
        return self.__str__()
