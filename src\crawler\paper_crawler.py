"""
论文爬取模块
"""

import asyncio
import aiohttp
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from urllib.parse import urlencode

from src.utils.logger import get_module_logger

logger = get_module_logger("crawler")


@dataclass
class Paper:
    """论文数据结构"""
    id: str
    title: str
    authors: List[str]
    abstract: str
    published_date: datetime
    updated_date: Optional[datetime]
    categories: List[str]
    pdf_url: Optional[str]
    source: str
    keywords: List[str] = None
    doi: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'authors': self.authors,
            'abstract': self.abstract,
            'published_date': self.published_date.isoformat(),
            'updated_date': self.updated_date.isoformat() if self.updated_date else None,
            'categories': self.categories,
            'pdf_url': self.pdf_url,
            'source': self.source,
            'keywords': self.keywords or [],
            'doi': self.doi
        }


class ArxivCrawler:
    """arXiv 论文爬虫"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = config.get('base_url', 'http://export.arxiv.org/api/query')
        self.max_results = config.get('max_results', 50)
        self.categories = config.get('categories', [])
        self.keywords = config.get('keywords', [])
    
    async def crawl(self, session: aiohttp.ClientSession, days_back: int = 7) -> List[Paper]:
        """
        爬取arXiv论文
        
        Args:
            session: HTTP会话
            days_back: 爬取最近几天的论文
            
        Returns:
            论文列表
        """
        papers = []
        
        try:
            # 构建查询参数
            query_parts = []
            
            # 添加分类查询
            if self.categories:
                cat_query = ' OR '.join([f'cat:{cat}' for cat in self.categories])
                query_parts.append(f'({cat_query})')
            
            # 添加关键词查询
            if self.keywords:
                keyword_query = ' OR '.join([f'all:"{kw}"' for kw in self.keywords])
                query_parts.append(f'({keyword_query})')
            
            # 添加时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            date_query = f'submittedDate:[{start_date.strftime("%Y%m%d")}0000 TO {end_date.strftime("%Y%m%d")}2359]'
            query_parts.append(date_query)
            
            query = ' AND '.join(query_parts)
            
            params = {
                'search_query': query,
                'start': 0,
                'max_results': self.max_results,
                'sortBy': 'submittedDate',
                'sortOrder': 'descending'
            }
            
            url = f"{self.base_url}?{urlencode(params)}"
            logger.info(f"开始爬取arXiv论文: {url}")
            
            async with session.get(url, timeout=30) as response:
                if response.status == 200:
                    content = await response.text()
                    papers = self._parse_arxiv_response(content)
                    logger.info(f"成功爬取 {len(papers)} 篇arXiv论文")
                elif response.status == 400:
                    logger.error(f"arXiv API请求参数错误: {response.status}")
                    logger.error(f"请求URL: {url}")
                elif response.status == 429:
                    logger.error(f"arXiv API请求频率过高: {response.status}")
                    # 可以在这里添加重试逻辑
                else:
                    logger.error(f"arXiv API请求失败: {response.status}")
                    error_text = await response.text()
                    logger.error(f"错误详情: {error_text[:500]}")
        
        except Exception as e:
            logger.error(f"arXiv爬取失败: {str(e)}")
        
        return papers
    
    def _parse_arxiv_response(self, xml_content: str) -> List[Paper]:
        """解析arXiv API响应"""
        papers = []
        
        try:
            root = ET.fromstring(xml_content)
            
            # 定义命名空间
            namespaces = {
                'atom': 'http://www.w3.org/2005/Atom',
                'arxiv': 'http://arxiv.org/schemas/atom'
            }
            
            entries = root.findall('atom:entry', namespaces)
            
            for entry in entries:
                try:
                    # 提取基本信息
                    id_elem = entry.find('atom:id', namespaces)
                    title_elem = entry.find('atom:title', namespaces)
                    summary_elem = entry.find('atom:summary', namespaces)
                    published_elem = entry.find('atom:published', namespaces)
                    updated_elem = entry.find('atom:updated', namespaces)
                    
                    if not all([id_elem, title_elem, summary_elem, published_elem]):
                        continue
                    
                    # 提取作者
                    authors = []
                    author_elems = entry.findall('atom:author', namespaces)
                    for author_elem in author_elems:
                        name_elem = author_elem.find('atom:name', namespaces)
                        if name_elem is not None:
                            authors.append(name_elem.text.strip())
                    
                    # 提取分类
                    categories = []
                    category_elems = entry.findall('atom:category', namespaces)
                    for cat_elem in category_elems:
                        term = cat_elem.get('term')
                        if term:
                            categories.append(term)
                    
                    # 提取PDF链接
                    pdf_url = None
                    link_elems = entry.findall('atom:link', namespaces)
                    for link_elem in link_elems:
                        if link_elem.get('type') == 'application/pdf':
                            pdf_url = link_elem.get('href')
                            break
                    
                    # 数据验证
                    title_text = title_elem.text.strip()
                    abstract_text = summary_elem.text.strip()

                    if len(title_text) < 10:  # 标题太短
                        logger.warning(f"跳过标题过短的论文: {title_text}")
                        continue

                    if len(abstract_text) < 50:  # 摘要太短
                        logger.warning(f"跳过摘要过短的论文: {title_text}")
                        continue

                    # 提取arXiv ID
                    arxiv_id = id_elem.text.split('/')[-1]
                    if not arxiv_id or len(arxiv_id) < 5:
                        logger.warning(f"无效的arXiv ID: {arxiv_id}")
                        continue

                    # 创建论文对象
                    paper = Paper(
                        id=arxiv_id,
                        title=title_text,
                        authors=authors,
                        abstract=abstract_text,
                        published_date=datetime.fromisoformat(published_elem.text.replace('Z', '+00:00')),
                        updated_date=datetime.fromisoformat(updated_elem.text.replace('Z', '+00:00')) if updated_elem is not None else None,
                        categories=categories,
                        pdf_url=pdf_url,
                        source='arxiv'
                    )
                    
                    papers.append(paper)
                    
                except Exception as e:
                    logger.warning(f"解析单篇论文失败: {str(e)}")
                    continue
        
        except Exception as e:
            logger.error(f"解析arXiv响应失败: {str(e)}")
        
        return papers


class PaperCrawler:
    """论文爬虫主类"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.crawlers = {}
        
        # 初始化各个爬虫
        crawler_config = self.config.get_section('crawler')
        sources = crawler_config.get('sources', {})
        
        if sources.get('arxiv', {}).get('enabled', False):
            self.crawlers['arxiv'] = ArxivCrawler(sources['arxiv'])
            logger.info("arXiv爬虫已启用")
        
        # TODO: 添加其他爬虫（PubMed等）
        
        self.strategy = crawler_config.get('strategy', {})
        self.filters = crawler_config.get('filters', {})
    
    async def crawl_papers(self) -> List[Paper]:
        """
        爬取所有配置的论文源
        
        Returns:
            论文列表
        """
        all_papers = []
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            
            for source_name, crawler in self.crawlers.items():
                logger.info(f"启动 {source_name} 爬虫")
                task = crawler.crawl(
                    session, 
                    self.strategy.get('date_range_days', 7)
                )
                tasks.append(task)
            
            # 并发执行所有爬虫
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                source_name = list(self.crawlers.keys())[i]
                if isinstance(result, Exception):
                    logger.error(f"{source_name} 爬虫执行失败: {str(result)}")
                else:
                    all_papers.extend(result)
                    logger.info(f"{source_name} 爬虫完成，获取 {len(result)} 篇论文")
        
        # 应用过滤器
        filtered_papers = self._apply_filters(all_papers)
        
        # 去重
        unique_papers = self._remove_duplicates(filtered_papers)
        
        logger.info(f"爬取完成，共获取 {len(unique_papers)} 篇有效论文")
        return unique_papers
    
    def _apply_filters(self, papers: List[Paper]) -> List[Paper]:
        """应用过滤规则"""
        filtered_papers = []
        
        min_abstract_length = self.filters.get('min_abstract_length', 0)
        exclude_keywords = self.filters.get('exclude_keywords', [])
        include_keywords = self.filters.get('include_keywords', [])
        
        for paper in papers:
            # 检查摘要长度
            if len(paper.abstract) < min_abstract_length:
                continue
            
            # 检查排除关键词
            if exclude_keywords:
                text_to_check = (paper.title + ' ' + paper.abstract).lower()
                if any(keyword.lower() in text_to_check for keyword in exclude_keywords):
                    continue
            
            # 检查包含关键词
            if include_keywords:
                text_to_check = (paper.title + ' ' + paper.abstract).lower()
                if not any(keyword.lower() in text_to_check for keyword in include_keywords):
                    continue
            
            filtered_papers.append(paper)
        
        logger.info(f"过滤后剩余 {len(filtered_papers)} 篇论文")
        return filtered_papers
    
    def _remove_duplicates(self, papers: List[Paper]) -> List[Paper]:
        """去除重复论文"""
        seen_titles = set()
        unique_papers = []
        
        for paper in papers:
            title_key = paper.title.lower().strip()
            if title_key not in seen_titles:
                seen_titles.add(title_key)
                unique_papers.append(paper)
        
        removed_count = len(papers) - len(unique_papers)
        if removed_count > 0:
            logger.info(f"去重完成，移除 {removed_count} 篇重复论文")
        
        return unique_papers
    
    async def test_connection(self) -> List[Paper]:
        """测试爬虫连接"""
        logger.info("开始测试爬虫连接")
        
        test_papers = []
        async with aiohttp.ClientSession() as session:
            for source_name, crawler in self.crawlers.items():
                try:
                    # 只爬取少量论文进行测试
                    if hasattr(crawler, 'max_results'):
                        original_max = crawler.max_results
                        crawler.max_results = 5
                    
                    papers = await crawler.crawl(session, days_back=1)
                    test_papers.extend(papers)
                    
                    # 恢复原始设置
                    if hasattr(crawler, 'max_results'):
                        crawler.max_results = original_max
                    
                    logger.info(f"{source_name} 连接测试成功")
                    
                except Exception as e:
                    logger.error(f"{source_name} 连接测试失败: {str(e)}")
        
        return test_papers
