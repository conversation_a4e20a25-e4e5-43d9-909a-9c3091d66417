"""
AI摘要生成模块
"""

import asyncio
import openai
import anthropic
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from src.crawler.paper_crawler import Paper
from src.utils.logger import get_module_logger

logger = get_module_logger("summarizer")


@dataclass
class PaperSummary:
    """论文摘要数据结构"""
    paper: Paper
    summary: str
    key_points: List[str]
    methodology: Optional[str]
    significance: Optional[str]
    limitations: Optional[str]
    generated_at: str
    model_used: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'paper': self.paper.to_dict(),
            'summary': self.summary,
            'key_points': self.key_points,
            'methodology': self.methodology,
            'significance': self.significance,
            'limitations': self.limitations,
            'generated_at': self.generated_at,
            'model_used': self.model_used
        }


class OpenAISummarizer:
    """OpenAI摘要生成器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'gpt-3.5-turbo')
        self.max_tokens = config.get('max_tokens', 500)
        self.temperature = config.get('temperature', 0.3)

        if self.api_key:
            self.client = openai.OpenAI(api_key=self.api_key)
        else:
            logger.warning("OpenAI API密钥未配置")
            self.client = None
    
    async def generate_summary(self, paper: Paper, language: str = "zh") -> Dict[str, Any]:
        """
        生成论文摘要

        Args:
            paper: 论文对象
            language: 摘要语言 (zh/en)

        Returns:
            摘要信息字典
        """
        if not self.client:
            raise ValueError("OpenAI API未正确配置")

        try:
            # 构建提示词
            prompt = self._build_prompt(paper, language)

            # 调用OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt(language)},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                timeout=30
            )

            content = response.choices[0].message.content
            return self._parse_response(content)

        except Exception as e:
            logger.error(f"OpenAI摘要生成失败: {str(e)}")
            raise
    
    def _build_prompt(self, paper: Paper, language: str) -> str:
        """构建提示词"""
        if language == "zh":
            prompt = f"""
请为以下学术论文生成详细的中文摘要：

标题: {paper.title}
作者: {', '.join(paper.authors)}
摘要: {paper.abstract}

请按以下格式输出：
1. 核心摘要（200-300字）
2. 关键要点（3-5个要点）
3. 研究方法
4. 学术意义
5. 局限性

请确保摘要准确、简洁且易于理解。
"""
        else:
            prompt = f"""
Please generate a detailed English summary for the following academic paper:

Title: {paper.title}
Authors: <AUTHORS>
Abstract: {paper.abstract}

Please output in the following format:
1. Core Summary (200-300 words)
2. Key Points (3-5 points)
3. Methodology
4. Significance
5. Limitations

Please ensure the summary is accurate, concise, and easy to understand.
"""
        return prompt
    
    def _get_system_prompt(self, language: str) -> str:
        """获取系统提示词"""
        if language == "zh":
            return "你是一个专业的学术论文摘要生成助手，擅长将复杂的学术内容转化为清晰易懂的中文摘要。"
        else:
            return "You are a professional academic paper summarization assistant, skilled at converting complex academic content into clear and understandable English summaries."
    
    def _parse_response(self, content: str) -> Dict[str, Any]:
        """解析AI响应"""
        # 简单的解析逻辑，可以根据需要改进
        lines = content.strip().split('\n')
        
        result = {
            'summary': '',
            'key_points': [],
            'methodology': '',
            'significance': '',
            'limitations': ''
        }
        
        current_section = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测段落标题
            if any(keyword in line.lower() for keyword in ['摘要', 'summary', '核心']):
                if current_section and current_content:
                    result[current_section] = '\n'.join(current_content)
                current_section = 'summary'
                current_content = []
            elif any(keyword in line.lower() for keyword in ['要点', 'key points', '关键']):
                if current_section and current_content:
                    result[current_section] = '\n'.join(current_content)
                current_section = 'key_points'
                current_content = []
            elif any(keyword in line.lower() for keyword in ['方法', 'methodology']):
                if current_section and current_content:
                    result[current_section] = '\n'.join(current_content)
                current_section = 'methodology'
                current_content = []
            elif any(keyword in line.lower() for keyword in ['意义', 'significance']):
                if current_section and current_content:
                    result[current_section] = '\n'.join(current_content)
                current_section = 'significance'
                current_content = []
            elif any(keyword in line.lower() for keyword in ['局限', 'limitations']):
                if current_section and current_content:
                    result[current_section] = '\n'.join(current_content)
                current_section = 'limitations'
                current_content = []
            else:
                if current_section:
                    current_content.append(line)
        
        # 处理最后一个段落
        if current_section and current_content:
            if current_section == 'key_points':
                # 将要点转换为列表
                result[current_section] = [point.strip('- ').strip() for point in current_content if point.strip()]
            else:
                result[current_section] = '\n'.join(current_content)
        
        return result


class ClaudeSummarizer:
    """Claude摘要生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'claude-3-sonnet-20240229')
        self.max_tokens = config.get('max_tokens', 500)
        
        if self.api_key:
            self.client = anthropic.Anthropic(api_key=self.api_key)
        else:
            logger.warning("Claude API密钥未配置")
            self.client = None
    
    async def generate_summary(self, paper: Paper, language: str = "zh") -> Dict[str, Any]:
        """生成论文摘要"""
        if not self.client:
            raise ValueError("Claude API未正确配置")

        try:
            prompt = self._build_prompt(paper, language)

            # Claude API调用（同步方式，因为anthropic库暂不支持异步）
            import asyncio
            loop = asyncio.get_event_loop()

            response = await loop.run_in_executor(
                None,
                lambda: self.client.messages.create(
                    model=self.model,
                    max_tokens=self.max_tokens,
                    messages=[
                        {"role": "user", "content": prompt}
                    ]
                )
            )

            content = response.content[0].text
            return self._parse_response(content)

        except Exception as e:
            logger.error(f"Claude摘要生成失败: {str(e)}")
            raise
    
    def _build_prompt(self, paper: Paper, language: str) -> str:
        """构建提示词（与OpenAI类似）"""
        # 复用OpenAI的提示词构建逻辑
        openai_summarizer = OpenAISummarizer({})
        return openai_summarizer._build_prompt(paper, language)
    
    def _parse_response(self, content: str) -> Dict[str, Any]:
        """解析AI响应（与OpenAI类似）"""
        openai_summarizer = OpenAISummarizer({})
        return openai_summarizer._parse_response(content)


class DeepSeekSummarizer:
    """DeepSeek摘要生成器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('api_key')
        self.base_url = config.get('base_url', 'https://api.deepseek.com/v1')
        self.model = config.get('model', 'deepseek-reasoner')
        self.max_tokens = config.get('max_tokens', 1000)
        self.temperature = config.get('temperature', 0.3)
        self.timeout = config.get('timeout', 60)

        if self.api_key:
            # 使用OpenAI兼容的客户端
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
        else:
            logger.warning("DeepSeek API密钥未配置")
            self.client = None
    
    async def generate_summary(self, paper: Paper, language: str = "zh") -> Dict[str, Any]:
        """
        生成论文摘要

        Args:
            paper: 论文对象
            language: 摘要语言 (zh/en)

        Returns:
            摘要信息字典
        """
        if not self.client:
            raise ValueError("DeepSeek API未正确配置")

        try:
            # 构建提示词
            prompt = self._build_prompt(paper, language)

            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt(language)},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                timeout=self.timeout
            )

            content = response.choices[0].message.content
            return self._parse_response(content)

        except Exception as e:
            logger.error(f"DeepSeek摘要生成失败: {str(e)}")
            raise
    
    def _build_prompt(self, paper: Paper, language: str) -> str:
        """构建提示词，针对DeepSeek Reasoner优化"""
        if language == "zh":
            prompt = f"""
请仔细分析以下学术论文，并生成详细的中文摘要。请运用你的推理能力深入理解论文内容：

标题: {paper.title}
作者: {', '.join(paper.authors)}
分类: {', '.join(paper.categories)}
摘要: {paper.abstract}

请按以下格式输出，每个部分都要深入分析：

1. 核心摘要（300-400字）
   - 清晰概述研究问题和解决方案
   - 突出创新点和贡献

2. 关键要点（4-6个要点）
   - 每个要点都要具体且有价值
   - 包含技术细节和实验结果

3. 研究方法
   - 详细描述使用的方法和技术
   - 分析方法的优势和适用性

4. 学术意义
   - 评估对相关领域的影响
   - 分析理论和实践价值

5. 局限性和未来方向
   - 客观分析研究的不足
   - 提出可能的改进方向

请确保分析深入、准确，体现出对论文的深度理解。
"""
        else:
            prompt = f"""
Please carefully analyze the following academic paper and generate a detailed English summary. Use your reasoning capabilities to deeply understand the paper content:

Title: {paper.title}
Authors: <AUTHORS>
Categories: {', '.join(paper.categories)}
Abstract: {paper.abstract}

Please output in the following format with in-depth analysis for each section:

1. Core Summary (300-400 words)
   - Clearly outline the research problem and solution
   - Highlight innovations and contributions

2. Key Points (4-6 points)
   - Each point should be specific and valuable
   - Include technical details and experimental results

3. Methodology
   - Detailed description of methods and techniques used
   - Analysis of advantages and applicability

4. Academic Significance
   - Evaluate impact on relevant fields
   - Analyze theoretical and practical value

5. Limitations and Future Directions
   - Objective analysis of research shortcomings
   - Propose possible improvement directions

Please ensure deep, accurate analysis that demonstrates thorough understanding of the paper.
"""
        return prompt
    
    def _get_system_prompt(self, language: str) -> str:
        """获取系统提示词，针对DeepSeek Reasoner优化"""
        if language == "zh":
            return """你是一个专业的学术论文分析专家，具有强大的推理能力。你擅长：
1. 深入理解复杂的学术内容
2. 识别研究的创新点和价值
3. 分析方法的优缺点
4. 评估研究的学术意义
5. 预测研究的发展方向

请运用你的推理能力，为用户提供深入、准确、有价值的论文摘要。"""
        else:
            return """You are a professional academic paper analysis expert with strong reasoning capabilities. You excel at:
1. Deeply understanding complex academic content
2. Identifying research innovations and value
3. Analyzing strengths and weaknesses of methods
4. Evaluating academic significance of research
5. Predicting research development directions

Please use your reasoning abilities to provide in-depth, accurate, and valuable paper summaries for users."""
    
    def _parse_response(self, content: str) -> Dict[str, Any]:
        """解析AI响应"""
        # 使用与OpenAI相同的解析逻辑，但可以根据DeepSeek的输出特点进行优化
        lines = content.strip().split('\n')
        
        result = {
            'summary': '',
            'key_points': [],
            'methodology': '',
            'significance': '',
            'limitations': ''
        }
        
        current_section = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测段落标题
            if any(keyword in line.lower() for keyword in ['摘要', 'summary', '核心', 'core']):
                if current_section and current_content:
                    if current_section == 'key_points':
                        result[current_section] = [point.strip('- ').strip() for point in current_content if point.strip()]
                    else:
                        result[current_section] = '\n'.join(current_content)
                current_section = 'summary'
                current_content = []
            elif any(keyword in line.lower() for keyword in ['要点', 'key points', '关键', 'key']):
                if current_section and current_content:
                    if current_section == 'key_points':
                        result[current_section] = [point.strip('- ').strip() for point in current_content if point.strip()]
                    else:
                        result[current_section] = '\n'.join(current_content)
                current_section = 'key_points'
                current_content = []
            elif any(keyword in line.lower() for keyword in ['方法', 'methodology', '研究方法']):
                if current_section and current_content:
                    if current_section == 'key_points':
                        result[current_section] = [point.strip('- ').strip() for point in current_content if point.strip()]
                    else:
                        result[current_section] = '\n'.join(current_content)
                current_section = 'methodology'
                current_content = []
            elif any(keyword in line.lower() for keyword in ['意义', 'significance', '学术意义']):
                if current_section and current_content:
                    if current_section == 'key_points':
                        result[current_section] = [point.strip('- ').strip() for point in current_content if point.strip()]
                    else:
                        result[current_section] = '\n'.join(current_content)
                current_section = 'significance'
                current_content = []
            elif any(keyword in line.lower() for keyword in ['局限', 'limitations', '未来方向', 'future']):
                if current_section and current_content:
                    if current_section == 'key_points':
                        result[current_section] = [point.strip('- ').strip() for point in current_content if point.strip()]
                    else:
                        result[current_section] = '\n'.join(current_content)
                current_section = 'limitations'
                current_content = []
            else:
                if current_section:
                    current_content.append(line)
        
        # 处理最后一个段落
        if current_section and current_content:
            if current_section == 'key_points':
                result[current_section] = [point.strip('- ').strip() for point in current_content if point.strip()]
            else:
                result[current_section] = '\n'.join(current_content)
        
        return result


class AISummarizer:
    """AI摘要生成器主类"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.summarizers = {}
        
        # 初始化各个摘要生成器
        summarizer_config = self.config.get_section('summarizer')
        default_provider = summarizer_config.get('default_provider', 'openai')
        
        # OpenAI
        openai_config = summarizer_config.get('openai', {})
        if openai_config.get('api_key'):
            self.summarizers['openai'] = OpenAISummarizer(openai_config)
            logger.info("OpenAI摘要生成器已启用")
        
        # Claude
        anthropic_config = summarizer_config.get('anthropic', {})
        if anthropic_config.get('api_key'):
            self.summarizers['anthropic'] = ClaudeSummarizer(anthropic_config)
            logger.info("Claude摘要生成器已启用")
        
        # DeepSeek
        deepseek_config = summarizer_config.get('deepseek', {})
        if deepseek_config.get('api_key'):
            self.summarizers['deepseek'] = DeepSeekSummarizer(deepseek_config)
            logger.info("DeepSeek摘要生成器已启用")
        
        self.default_provider = default_provider
        self.summary_config = summarizer_config.get('summary', {})
    
    async def generate_summaries(self, papers: List[Paper]) -> List[PaperSummary]:
        """
        为论文列表生成摘要
        
        Args:
            papers: 论文列表
            
        Returns:
            论文摘要列表
        """
        summaries = []
        language = self.summary_config.get('language', 'zh')
        
        # 创建并发任务
        semaphore = asyncio.Semaphore(5)  # 限制并发数
        tasks = []
        
        for paper in papers:
            task = self._generate_single_summary(semaphore, paper, language)
            tasks.append(task)
        
        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"论文 {papers[i].title} 摘要生成失败: {str(result)}")
            else:
                summaries.append(result)
        
        logger.info(f"成功生成 {len(summaries)} 篇论文摘要")
        return summaries
    
    async def _generate_single_summary(self, semaphore: asyncio.Semaphore, paper: Paper, language: str) -> PaperSummary:
        """生成单篇论文摘要"""
        async with semaphore:
            try:
                # 选择摘要生成器
                provider = self.default_provider
                if provider not in self.summarizers:
                    provider = list(self.summarizers.keys())[0] if self.summarizers else None
                
                if not provider:
                    raise ValueError("没有可用的摘要生成器")
                
                summarizer = self.summarizers[provider]
                
                # 生成摘要
                summary_data = await summarizer.generate_summary(paper, language)
                
                # 创建摘要对象
                from datetime import datetime
                summary = PaperSummary(
                    paper=paper,
                    summary=summary_data.get('summary', ''),
                    key_points=summary_data.get('key_points', []),
                    methodology=summary_data.get('methodology', ''),
                    significance=summary_data.get('significance', ''),
                    limitations=summary_data.get('limitations', ''),
                    generated_at=datetime.now().isoformat(),
                    model_used=provider
                )
                
                logger.debug(f"论文 {paper.title} 摘要生成完成")
                return summary
                
            except Exception as e:
                logger.error(f"论文 {paper.title} 摘要生成失败: {str(e)}")
                raise
    
    async def test_api(self) -> bool:
        """测试API连接"""
        logger.info("开始测试摘要生成API")
        
        # 创建测试论文
        test_paper = Paper(
            id="test",
            title="Test Paper",
            authors=["Test Author"],
            abstract="This is a test abstract for API testing purposes.",
            published_date=datetime.now(),
            updated_date=None,
            categories=["test"],
            pdf_url=None,
            source="test"
        )
        
        for provider, summarizer in self.summarizers.items():
            try:
                result = await summarizer.generate_summary(test_paper, "en")
                if result and result.get('summary'):
                    logger.info(f"{provider} API测试成功")
                    return True
                else:
                    logger.warning(f"{provider} API返回空结果")
            except Exception as e:
                logger.error(f"{provider} API测试失败: {str(e)}")

        return False
