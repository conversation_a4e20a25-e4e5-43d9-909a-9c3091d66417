<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最新论文摘要 - 最新论文摘要</title>
    <meta name="description" content="AI自动生成的最新学术论文摘要">
    <meta name="author" content="论文自动化系统">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --font-family: Inter, sans-serif;
        }
        
        body {
            font-family: var(--font-family);
            line-height: 1.6;
            color: #333;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .paper-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .paper-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .paper-title {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }
        
        .paper-title:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }
        
        .paper-authors {
            color: var(--secondary-color);
            font-size: 0.9rem;
        }
        
        .paper-summary {
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .category-badge {
            background-color: var(--primary-color);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
        }
        
        .category-badge:hover {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .key-points {
            background-color: #f8f9fa;
            border-left: 4px solid var(--primary-color);
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .key-points h6 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .key-points ul {
            margin-bottom: 0;
            padding-left: 1.2rem;
        }
        
        .footer {
            background-color: #f8f9fa;
            color: var(--secondary-color);
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        .search-box {
            max-width: 400px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
                color: #e5e5e5;
            }
            
            .paper-card {
                background-color: #2d2d2d;
                color: #e5e5e5;
            }
            
            .navbar {
                background-color: #2d2d2d !important;
            }
            
            .footer {
                background-color: #2d2d2d;
            }
        }
        
    </style>
    
    
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-graduation-cap me-2"></i>
                最新论文摘要
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-tags me-1"></i>分类
                        </a>
                        <ul class="dropdown-menu">
                            
                            <li><a class="dropdown-item" href="/categories/cs.AI.html">cs.AI</a></li>
                            
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/rss.xml" target="_blank">
                            <i class="fas fa-rss me-1"></i>RSS
                        </a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <div class="d-flex search-box">
                    <input class="form-control me-2" type="search" placeholder="搜索论文..." id="searchInput">
                    <button class="btn btn-outline-primary" type="button" onclick="searchPapers()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="container my-4">
        
<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="stats-number">1</div>
            <div>篇论文</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="stats-number">1</div>
            <div>个分类</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="stats-number">1</div>
            <div>个来源</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card text-center">
            <div class="stats-number">
                
                06-08
                
            </div>
            <div>最新更新</div>
        </div>
    </div>
</div>

<!-- 快速导航 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-tags me-2"></i>热门分类
                </h5>
                <div class="d-flex flex-wrap gap-2">
                    
                    <a href="/categories/cs.AI.html" class="category-badge">
                        cs.AI (1)
                    </a>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 论文列表 -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2>
                <i class="fas fa-newspaper me-2"></i>最新论文摘要
            </h2>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortPapers('date')">
                    <i class="fas fa-calendar me-1"></i>按日期
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortPapers('title')">
                    <i class="fas fa-sort-alpha-down me-1"></i>按标题
                </button>
            </div>
        </div>
        
        <div id="papers-container">
            
            <div class="card paper-card mb-4" data-date="2025-06-08T14:57:24.999328" data-title="测试论文标题">
                <div class="card-body">
                    <!-- 论文标题 -->
                    <h5 class="card-title">
                        <a href="/papers/test-001.html" class="paper-title">
                            测试论文标题
                        </a>
                    </h5>
                    
                    <!-- 作者和元信息 -->
                    <div class="paper-authors mb-2">
                        <i class="fas fa-users me-1"></i>
                        测试作者1, 测试作者2
                        <span class="ms-3">
                            <i class="fas fa-calendar me-1"></i>
                            2025-06-08
                        </span>
                        <span class="ms-3">
                            <i class="fas fa-source me-1"></i>
                            test
                        </span>
                    </div>
                    
                    <!-- 分类标签 -->
                    <div class="mb-3">
                        
                        <a href="/categories/cs.AI.html" class="category-badge me-1">
                            cs.AI
                        </a>
                        
                    </div>
                    
                    <!-- AI摘要 -->
                    <div class="paper-summary mb-3">
                        <strong>AI摘要:</strong><br>
                        这是一个测试摘要。
                    </div>
                    
                    <!-- 关键要点 -->
                    
                    <div class="key-points">
                        <h6>
                            <i class="fas fa-lightbulb me-1"></i>关键要点:
                        </h6>
                        <ul class="mb-0">
                            
                            <li>要点1</li>
                            
                            <li>要点2</li>
                            
                            <li>要点3</li>
                            
                        </ul>
                    </div>
                    
                    
                    <!-- 操作按钮 -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            
                            <a href="https://example.com/test.pdf" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-file-pdf me-1"></i>查看PDF
                            </a>
                            
                            <a href="/papers/test-001.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>详细信息
                            </a>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-robot me-1"></i>
                            由 test 生成
                        </small>
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- 分页 -->
        
    </div>
</div>

<!-- 空状态 -->


    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>最新论文摘要</h6>
                    <p class="mb-0">AI自动生成的最新学术论文摘要</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-robot me-1"></i>
                        由AI自动生成 | 
                        <i class="fas fa-clock me-1"></i>
                        更新时间: 
                    </p>
                    <p class="mb-0">
                        <a href="https://github.com" target="_blank" class="text-decoration-none">
                            <i class="fab fa-github me-1"></i>GitHub
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 搜索功能
        function searchPapers() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const papers = document.querySelectorAll('.paper-card');
            
            papers.forEach(paper => {
                const title = paper.querySelector('.paper-title').textContent.toLowerCase();
                const summary = paper.querySelector('.paper-summary').textContent.toLowerCase();
                const authors = paper.querySelector('.paper-authors').textContent.toLowerCase();
                
                if (title.includes(query) || summary.includes(query) || authors.includes(query)) {
                    paper.style.display = 'block';
                } else {
                    paper.style.display = 'none';
                }
            });
        }
        
        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchPapers();
            }
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
    
    
<script>
// 排序功能
function sortPapers(sortBy) {
    const container = document.getElementById('papers-container');
    const papers = Array.from(container.children);
    
    papers.sort((a, b) => {
        if (sortBy === 'date') {
            const dateA = new Date(a.dataset.date);
            const dateB = new Date(b.dataset.date);
            return dateB - dateA; // 降序
        } else if (sortBy === 'title') {
            const titleA = a.dataset.title.toLowerCase();
            const titleB = b.dataset.title.toLowerCase();
            return titleA.localeCompare(titleB); // 升序
        }
    });
    
    // 重新排列DOM元素
    papers.forEach(paper => container.appendChild(paper));
    
    // 更新按钮状态
    document.querySelectorAll('.btn-group button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

// 懒加载图片（如果有的话）
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// 添加阅读进度指示器
window.addEventListener('scroll', () => {
    const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
    const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrolled = (winScroll / height) * 100;
    
    // 如果有进度条元素的话
    const progressBar = document.getElementById('reading-progress');
    if (progressBar) {
        progressBar.style.width = scrolled + '%';
    }
});
</script>

</body>
</html>