#!/usr/bin/env python3
"""
基本使用示例
演示如何使用论文自动化处理系统的各个模块
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.config import ConfigManager
from src.utils.database import PaperDatabase
from src.crawler.paper_crawler import PaperCrawler
from src.summarizer.ai_summarizer import AISummarizer


async def example_1_basic_crawling():
    """示例1: 基础论文爬取"""
    print("📡 示例1: 基础论文爬取")
    print("-" * 30)
    
    # 初始化配置和爬虫
    config = ConfigManager("config/config.yaml")
    crawler = PaperCrawler(config)
    
    # 爬取论文
    papers = await crawler.crawl_papers()
    
    print(f"✅ 成功爬取 {len(papers)} 篇论文")
    
    # 显示前3篇论文信息
    for i, paper in enumerate(papers[:3], 1):
        print(f"\n📄 论文 {i}:")
        print(f"   标题: {paper.title}")
        print(f"   作者: {', '.join(paper.authors[:2])}{'...' if len(paper.authors) > 2 else ''}")
        print(f"   分类: {', '.join(paper.categories)}")
        print(f"   发布: {paper.published_date.strftime('%Y-%m-%d')}")
        print(f"   摘要: {paper.abstract[:100]}...")
    
    return papers


async def example_2_ai_summarization(papers):
    """示例2: AI摘要生成"""
    print("\n\n🤖 示例2: AI摘要生成")
    print("-" * 30)
    
    if not papers:
        print("⚠️ 没有论文可供摘要，跳过此示例")
        return []
    
    # 初始化AI摘要生成器
    config = ConfigManager("config/config.yaml")
    summarizer = AISummarizer(config)
    
    # 检查API配置
    if not summarizer.summarizers:
        print("⚠️ 未配置AI API密钥，跳过摘要生成")
        print("请设置 OPENAI_API_KEY 或 ANTHROPIC_API_KEY 环境变量")
        return []
    
    # 只对第一篇论文生成摘要（节省API调用）
    test_paper = papers[0]
    print(f"📄 正在为论文生成摘要: {test_paper.title[:50]}...")
    
    try:
        summaries = await summarizer.generate_summaries([test_paper])
        
        if summaries:
            summary = summaries[0]
            print(f"✅ 摘要生成成功")
            print(f"\n📝 AI摘要:")
            print(f"   {summary.summary}")
            
            if summary.key_points:
                print(f"\n💡 关键要点:")
                for i, point in enumerate(summary.key_points, 1):
                    print(f"   {i}. {point}")
            
            print(f"\n🔧 生成信息:")
            print(f"   使用模型: {summary.model_used}")
            print(f"   生成时间: {summary.generated_at}")
            
            return summaries
        else:
            print("❌ 摘要生成失败")
            return []
            
    except Exception as e:
        print(f"❌ 摘要生成异常: {str(e)}")
        return []


async def example_3_database_operations(papers, summaries):
    """示例3: 数据库操作"""
    print("\n\n💾 示例3: 数据库操作")
    print("-" * 30)
    
    # 初始化数据库
    db = PaperDatabase("data/example_papers.db")
    
    # 保存论文
    if papers:
        print(f"📥 保存 {len(papers)} 篇论文到数据库...")
        saved_count = db.save_papers(papers)
        print(f"✅ 成功保存 {saved_count} 篇论文")
    
    # 保存摘要
    if summaries:
        print(f"📥 保存 {len(summaries)} 篇摘要到数据库...")
        for summary in summaries:
            db.save_summary(summary)
        print(f"✅ 摘要保存完成")
    
    # 查询统计信息
    stats = db.get_statistics()
    print(f"\n📊 数据库统计:")
    print(f"   总论文数: {stats.get('total_papers', 0)}")
    print(f"   总摘要数: {stats.get('total_summaries', 0)}")
    print(f"   数据源: {stats.get('sources', {})}")
    
    # 查询最近论文
    recent_papers = db.get_recent_papers(days=7)
    print(f"   最近7天论文: {len(recent_papers)} 篇")
    
    return db


async def example_4_custom_filtering():
    """示例4: 自定义过滤"""
    print("\n\n🔍 示例4: 自定义过滤")
    print("-" * 30)
    
    # 创建自定义配置
    config = ConfigManager("config/config.yaml")
    
    # 修改过滤条件
    original_keywords = config.get('crawler.sources.arxiv.keywords', [])
    config.set('crawler.sources.arxiv.keywords', ['neural network', 'transformer'])
    config.set('crawler.sources.arxiv.max_results', 3)  # 只爬取3篇
    
    print("🎯 使用自定义过滤条件:")
    print(f"   关键词: {config.get('crawler.sources.arxiv.keywords')}")
    print(f"   最大结果: {config.get('crawler.sources.arxiv.max_results')}")
    
    # 爬取论文
    crawler = PaperCrawler(config)
    filtered_papers = await crawler.crawl_papers()
    
    print(f"✅ 过滤后获得 {len(filtered_papers)} 篇论文")
    
    # 显示结果
    for i, paper in enumerate(filtered_papers, 1):
        print(f"\n📄 过滤论文 {i}:")
        print(f"   标题: {paper.title}")
        print(f"   匹配关键词: {[kw for kw in config.get('crawler.sources.arxiv.keywords') if kw.lower() in paper.title.lower() or kw.lower() in paper.abstract.lower()]}")
    
    # 恢复原始配置
    config.set('crawler.sources.arxiv.keywords', original_keywords)
    
    return filtered_papers


async def main():
    """主函数 - 运行所有示例"""
    print("🚀 论文自动化处理系统 - 使用示例")
    print("=" * 50)
    
    try:
        # 示例1: 基础爬取
        papers = await example_1_basic_crawling()
        
        # 示例2: AI摘要
        summaries = await example_2_ai_summarization(papers)
        
        # 示例3: 数据库操作
        db = await example_3_database_operations(papers, summaries)
        
        # 示例4: 自定义过滤
        filtered_papers = await example_4_custom_filtering()
        
        print("\n" + "=" * 50)
        print("🎉 所有示例运行完成！")
        print("\n📁 生成的文件:")
        print("   - 数据库: data/example_papers.db")
        print("   - 日志: logs/app.log")
        
        print("\n💡 下一步:")
        print("   1. 查看数据库: sqlite3 data/example_papers.db")
        print("   2. 运行完整系统: python run.py --mode full")
        print("   3. 查看更多示例: examples/")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {str(e)}")
        print("请检查:")
        print("   1. 网络连接是否正常")
        print("   2. 配置文件是否存在")
        print("   3. API密钥是否配置（用于摘要生成）")


if __name__ == "__main__":
    # 创建必要目录
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # 运行示例
    asyncio.run(main())
