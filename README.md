# 论文自动化处理系统

基于 GitHub Actions 的论文自动化处理系统，能够自动爬取最新论文、生成AI摘要、渲染网页并推送邮件通知。

## 🎯 项目目标

开发一个完全自动化的论文处理流水线，帮助研究人员及时获取和了解最新的学术动态。

## 🏗️ 系统架构

```
paper-automation-system/
├── src/
│   ├── crawler/          # 论文爬取模块
│   ├── summarizer/       # AI摘要生成模块
│   ├── renderer/         # 网页渲染模块
│   ├── mailer/          # 邮件推送模块
│   └── utils/           # 通用工具模块
├── .github/
│   └── workflows/       # GitHub Actions 工作流
├── config/              # 配置文件
├── templates/           # 网页模板
├── output/              # 输出文件
└── docs/               # 项目文档
```

## 🚀 核心功能

### 1. 论文爬取模块
- 支持多个学术网站（arXiv、PubMed、IEEE等）
- 可配置的爬取规则和关键词过滤
- 增量更新，避免重复爬取

### 2. AI摘要生成模块
- 集成多个大语言模型API（OpenAI、Claude、本地模型等）
- 智能摘要生成和关键信息提取
- 支持批量处理和错误重试

### 3. 网页渲染模块
- 响应式网页设计
- 论文分类和搜索功能
- 静态网站生成，支持GitHub Pages

### 4. 邮件推送模块
- 定制化邮件模板
- 支持多种邮件服务提供商
- 智能推送策略（摘要、全文、链接等）

## 📋 开发任务清单

### Phase 1: 基础框架搭建 ✅
- [x] 项目结构初始化
- [x] README.md 编写
- [x] CHANGELOG.md 创建
- [ ] 基础配置文件设置
- [ ] 依赖管理配置

### Phase 2: 论文爬取模块
- [ ] arXiv API 集成
- [ ] PubMed API 集成
- [ ] 通用爬虫框架
- [ ] 数据存储和去重
- [ ] 配置化爬取规则

### Phase 3: AI摘要生成模块
- [ ] OpenAI API 集成
- [ ] Claude API 集成
- [ ] 本地模型支持
- [ ] 摘要质量评估
- [ ] 批量处理优化

### Phase 4: 网页渲染模块
- [ ] 静态网站生成器
- [ ] 响应式模板设计
- [ ] 搜索和过滤功能
- [ ] GitHub Pages 集成

### Phase 5: 邮件推送模块
- [ ] SMTP 配置
- [ ] 邮件模板设计
- [ ] 推送策略配置
- [ ] 订阅管理系统

### Phase 6: GitHub Actions 集成
- [ ] 定时触发工作流
- [ ] 环境变量配置
- [ ] 错误处理和通知
- [ ] 部署自动化

### Phase 7: 测试和优化
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能优化
- [ ] 文档完善

## 🛠️ 技术栈

- **语言**: Python 3.9+
- **爬虫**: requests, BeautifulSoup4, scrapy
- **AI集成**: openai, anthropic, transformers
- **网页生成**: Jinja2, Bootstrap
- **邮件**: smtplib, email
- **自动化**: GitHub Actions
- **数据存储**: JSON, SQLite
- **部署**: GitHub Pages

## 📦 安装和使用

### 环境要求
- Python 3.9 或更高版本
- Git
- GitHub 账户

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd paper-automation-system
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp config/config.example.yaml config/config.yaml
# 编辑配置文件，填入必要的API密钥和配置
```

4. **本地测试**
```bash
python src/main.py --test
```

5. **部署到GitHub Actions**
- 在GitHub仓库中设置必要的Secrets
- 推送代码触发自动化流程

## ⚙️ 配置说明

### API密钥配置
在GitHub仓库的Settings > Secrets中配置以下环境变量：
- `OPENAI_API_KEY`: OpenAI API密钥
- `ANTHROPIC_API_KEY`: Claude API密钥
- `EMAIL_PASSWORD`: 邮件服务密码
- `SMTP_SERVER`: SMTP服务器地址

### 爬取配置
编辑 `config/crawler.yaml` 文件：
- 设置目标网站和关键词
- 配置爬取频率和数量限制
- 设置过滤规则

### 邮件配置
编辑 `config/mailer.yaml` 文件：
- 配置SMTP服务器信息
- 设置收件人列表
- 自定义邮件模板

## 🔧 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确配置
   - 确认API配额是否充足
   - 查看网络连接状态

2. **爬取失败**
   - 检查目标网站是否可访问
   - 确认爬取规则是否正确
   - 查看是否被反爬虫机制阻止

3. **邮件发送失败**
   - 检查SMTP配置是否正确
   - 确认邮箱密码和权限
   - 查看防火墙设置

### 日志查看
```bash
# 查看最新日志
tail -f logs/app.log

# 查看特定模块日志
grep "crawler" logs/app.log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 更新 CHANGELOG.md
5. 推送到分支 (`git push origin feature/AmazingFeature`)
6. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件至 [<EMAIL>]

---

**注意**: 本项目仍在开发中，功能可能不完整。欢迎贡献代码和提出建议！
