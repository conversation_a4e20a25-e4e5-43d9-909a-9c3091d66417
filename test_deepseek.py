#!/usr/bin/env python3
"""
DeepSeek API专用测试脚本
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.config import ConfigManager
from src.summarizer.ai_summarizer import DeepSeekSummarizer
from src.crawler.paper_crawler import Paper


async def test_deepseek_api():
    """测试DeepSeek API连接和功能"""
    print("🤖 测试DeepSeek API...")
    
    try:
        # 加载配置
        config = ConfigManager("config/config.yaml")
        deepseek_config = config.get_section('summarizer').get('deepseek', {})
        
        # 检查API密钥
        api_key = deepseek_config.get('api_key')
        if not api_key:
            print("❌ DeepSeek API密钥未配置")
            print("请在config/config.yaml中配置deepseek.api_key")
            return False
        
        print(f"✅ API密钥已配置: {api_key[:10]}...")
        print(f"✅ 模型: {deepseek_config.get('model', 'deepseek-reasoner')}")
        print(f"✅ 基础URL: {deepseek_config.get('base_url', 'https://api.deepseek.com/v1')}")
        
        # 初始化DeepSeek摘要生成器
        summarizer = DeepSeekSummarizer(deepseek_config)
        
        if not summarizer.client:
            print("❌ DeepSeek客户端初始化失败")
            return False
        
        print("✅ DeepSeek客户端初始化成功")
        
        # 创建测试论文
        test_paper = Paper(
            id="test-deepseek-001",
            title="Attention Is All You Need",
            authors=["Ashish Vaswani", "Noam Shazeer", "Niki Parmar"],
            abstract="""The dominant sequence transduction models are based on complex recurrent or convolutional neural networks that include an encoder and a decoder. The best performing models also connect the encoder and decoder through an attention mechanism. We propose a new simple network architecture, the Transformer, based solely on attention mechanisms, dispensing with recurrence and convolutions entirely. Experiments on two machine translation tasks show that these models are superior in quality while being more parallelizable and requiring significantly less time to train.""",
            published_date=datetime.now(),
            updated_date=None,
            categories=["cs.CL", "cs.AI"],
            pdf_url="https://arxiv.org/pdf/1706.03762.pdf",
            source="test"
        )
        
        print(f"\n📄 测试论文: {test_paper.title}")
        print(f"📝 摘要长度: {len(test_paper.abstract)} 字符")
        
        # 测试中文摘要生成
        print("\n🔄 生成中文摘要...")
        try:
            zh_result = await summarizer.generate_summary(test_paper, "zh")
            
            print("✅ 中文摘要生成成功!")
            print(f"\n📝 核心摘要:")
            print(f"   {zh_result.get('summary', '未生成')}")
            
            if zh_result.get('key_points'):
                print(f"\n💡 关键要点:")
                for i, point in enumerate(zh_result['key_points'], 1):
                    print(f"   {i}. {point}")
            
            if zh_result.get('methodology'):
                print(f"\n🔧 技术方法:")
                print(f"   {zh_result['methodology']}")
            
            if zh_result.get('significance'):
                print(f"\n⭐ 学术意义:")
                print(f"   {zh_result['significance']}")
            
            if zh_result.get('reasoning_analysis'):
                print(f"\n🧠 推理分析:")
                print(f"   {zh_result['reasoning_analysis']}")
                
        except Exception as e:
            print(f"❌ 中文摘要生成失败: {str(e)}")
            return False
        
        # 测试英文摘要生成
        print("\n🔄 生成英文摘要...")
        try:
            en_result = await summarizer.generate_summary(test_paper, "en")
            
            print("✅ 英文摘要生成成功!")
            print(f"\n📝 Core Summary:")
            print(f"   {en_result.get('summary', 'Not generated')}")
            
            if en_result.get('key_points'):
                print(f"\n💡 Key Points:")
                for i, point in enumerate(en_result['key_points'], 1):
                    print(f"   {i}. {point}")
                    
        except Exception as e:
            print(f"❌ 英文摘要生成失败: {str(e)}")
            return False
        
        print("\n🎉 DeepSeek API测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek API测试失败: {str(e)}")
        return False


async def test_deepseek_vs_others():
    """对比DeepSeek与其他AI服务的效果"""
    print("\n🔄 对比测试DeepSeek与其他AI服务...")
    
    try:
        config = ConfigManager("config/config.yaml")
        
        # 创建测试论文
        test_paper = Paper(
            id="compare-test-001",
            title="BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding",
            authors=["Jacob Devlin", "Ming-Wei Chang", "Kenton Lee"],
            abstract="""We introduce a new language representation model called BERT, which stands for Bidirectional Encoder Representations from Transformers. Unlike recent language representation models, BERT is designed to pre-train deep bidirectional representations from unlabeled text by jointly conditioning on both left and right context in all layers.""",
            published_date=datetime.now(),
            updated_date=None,
            categories=["cs.CL"],
            pdf_url="https://arxiv.org/pdf/1810.04805.pdf",
            source="test"
        )
        
        print(f"📄 对比测试论文: {test_paper.title}")
        
        # 测试DeepSeek
        deepseek_config = config.get_section('summarizer').get('deepseek', {})
        if deepseek_config.get('api_key'):
            print("\n🤖 DeepSeek分析:")
            summarizer = DeepSeekSummarizer(deepseek_config)
            result = await summarizer.generate_summary(test_paper, "zh")
            print(f"   摘要长度: {len(result.get('summary', ''))} 字符")
            print(f"   关键要点数: {len(result.get('key_points', []))}")
            print(f"   是否包含推理分析: {'是' if result.get('reasoning_analysis') else '否'}")
        
        # 测试OpenAI（如果配置了）
        from src.summarizer.ai_summarizer import OpenAISummarizer
        openai_config = config.get_section('summarizer').get('openai', {})
        if openai_config.get('api_key'):
            print("\n🔵 OpenAI分析:")
            try:
                summarizer = OpenAISummarizer(openai_config)
                result = await summarizer.generate_summary(test_paper, "zh")
                print(f"   摘要长度: {len(result.get('summary', ''))} 字符")
                print(f"   关键要点数: {len(result.get('key_points', []))}")
            except Exception as e:
                print(f"   ❌ OpenAI测试失败: {str(e)}")
        
        print("\n✅ 对比测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {str(e)}")
        return False


async def main():
    """主函数"""
    print("🧪 DeepSeek API专用测试")
    print("=" * 40)
    
    # 创建必要目录
    Path("logs").mkdir(exist_ok=True)
    
    # 检查配置文件
    config_file = Path("config/config.yaml")
    if not config_file.exists():
        print("❌ 配置文件不存在: config/config.yaml")
        print("请先运行: cp config/config.example.yaml config/config.yaml")
        return
    
    # 运行测试
    tests = [
        ("DeepSeek API基础测试", test_deepseek_api),
        ("DeepSeek对比测试", test_deepseek_vs_others),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"🔬 {test_name}")
        print(f"{'='*40}")
        
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}异常: {str(e)}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 40)
    print("📋 测试结果汇总:")
    print("=" * 40)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 DeepSeek集成测试通过！")
        print("\n💡 使用建议:")
        print("   - DeepSeek Reasoner模型特别适合深度分析")
        print("   - 推理分析功能提供独特的学术洞察")
        print("   - 可以在config.yaml中设置为默认provider")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        print("\n🔧 故障排除:")
        print("   - 检查DeepSeek API密钥是否正确")
        print("   - 确认网络可以访问api.deepseek.com")
        print("   - 验证API配额是否充足")


if __name__ == "__main__":
    asyncio.run(main())
