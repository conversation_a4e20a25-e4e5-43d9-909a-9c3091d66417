#!/usr/bin/env python3
"""
论文自动化处理系统快速启动脚本
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.9或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查配置文件
    config_file = Path("config/config.yaml")
    if not config_file.exists():
        print("⚠️ 配置文件不存在，使用示例配置")
        example_file = Path("config/config.example.yaml")
        if example_file.exists():
            import shutil
            shutil.copy2(example_file, config_file)
            print("✅ 已创建默认配置文件")
        else:
            print("❌ 示例配置文件不存在")
            return False
    
    # 检查必要目录
    directories = ["logs", "output", "data"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✅ 目录结构检查完成")
    
    return True


def check_api_keys():
    """检查API密钥配置"""
    print("\n🔑 检查API密钥配置...")
    
    openai_key = os.getenv('OPENAI_API_KEY')
    deepseek_key = os.getenv('DEEPSEEK_API_KEY')
    claude_key = os.getenv('ANTHROPIC_API_KEY')

    if openai_key:
        print("✅ OpenAI API密钥已配置")
        return True
    elif deepseek_key:
        print("✅ DeepSeek API密钥已配置")
        return True
    elif claude_key:
        print("✅ Claude API密钥已配置")
        return True
    else:
        print("⚠️ 未检测到AI API密钥")
        print("请设置环境变量:")
        print("  export OPENAI_API_KEY='your-openai-key'")
        print("  或")
        print("  export DEEPSEEK_API_KEY='your-deepseek-key'")
        print("  或")
        print("  export ANTHROPIC_API_KEY='your-claude-key'")
        print("\n或在config/config.yaml中直接配置")
        return False





async def run_crawler_only():
    """只运行爬虫模块"""
    print("\n📡 运行论文爬虫...")
    
    try:
        from src.main import PaperAutomationSystem
        
        system = PaperAutomationSystem("config/config.yaml")
        papers = await system.crawler.crawl_papers()
        
        print(f"✅ 成功爬取 {len(papers)} 篇论文")
        
        if papers:
            # 保存到数据库
            saved_count = system.database.save_papers(papers)
            print(f"✅ 保存了 {saved_count} 篇论文到数据库")
            
            # 显示示例
            paper = papers[0]
            print(f"\n📄 示例论文:")
            print(f"   标题: {paper.title}")
            print(f"   作者: {', '.join(paper.authors[:2])}{'...' if len(paper.authors) > 2 else ''}")
            print(f"   分类: {', '.join(paper.categories)}")
            print(f"   发布: {paper.published_date.strftime('%Y-%m-%d')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬虫运行失败: {str(e)}")
        return False


async def run_full_pipeline():
    """运行完整流水线"""
    print("\n🔄 运行完整流水线...")
    
    try:
        from src.main import PaperAutomationSystem
        
        system = PaperAutomationSystem("config/config.yaml")
        await system.run_full_pipeline()
        
        print("✅ 完整流水线执行完成")
        return True
        
    except Exception as e:
        print(f"❌ 流水线执行失败: {str(e)}")
        return False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="论文自动化处理系统快速启动")
    parser.add_argument(
        "--mode",
        choices=["crawler", "full"],
        default="crawler",
        help="运行模式: crawler(仅爬虫), full(完整流水线)"
    )
    parser.add_argument(
        "--skip-checks", 
        action="store_true",
        help="跳过环境检查"
    )
    
    args = parser.parse_args()
    
    print("🚀 论文自动化处理系统")
    print("=" * 40)
    
    # 环境检查
    if not args.skip_checks:
        if not check_environment():
            print("❌ 环境检查失败")
            return
        
        api_configured = check_api_keys()
        if not api_configured and args.mode == "full":
            print("❌ 完整流水线需要配置AI API密钥")
            return

    # 根据模式运行
    success = False

    if args.mode == "crawler":
        success = await run_crawler_only()
    elif args.mode == "full":
        success = await run_full_pipeline()
    
    # 输出结果
    print("\n" + "=" * 40)
    if success:
        print("🎉 运行成功完成！")
        
        if args.mode == "full":
            print("\n📁 输出文件:")
            print("   - 网页文件: output/")
            print("   - 数据库: data/papers.db")
            print("   - 日志文件: logs/app.log")
        elif args.mode == "crawler":
            print("\n📁 输出文件:")
            print("   - 数据库: data/papers.db")
            print("   - 日志文件: logs/app.log")
        
        print("\n🔗 有用的命令:")
        print("   查看日志: tail -f logs/app.log")
        print("   查看数据库: sqlite3 data/papers.db")
        if args.mode == "full":
            print("   查看网页: open output/index.html")
    else:
        print("❌ 运行失败")
        print("\n🔧 故障排除:")
        print("   1. 检查网络连接")
        print("   2. 验证API密钥配置")
        print("   3. 查看日志文件: logs/app.log")
        print("   4. 先运行爬虫模式: python run.py --mode crawler")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        sys.exit(1)
