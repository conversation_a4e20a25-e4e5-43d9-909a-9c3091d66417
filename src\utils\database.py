"""
数据存储模块
"""

import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from src.crawler.paper_crawler import Paper
    from src.summarizer.ai_summarizer import PaperSummary

from src.utils.logger import get_module_logger

logger = get_module_logger("database")


class PaperDatabase:
    """论文数据库管理器"""
    
    def __init__(self, db_path: str = "data/papers.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建论文表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS papers (
                        id TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        authors TEXT NOT NULL,
                        abstract TEXT NOT NULL,
                        published_date TEXT NOT NULL,
                        updated_date TEXT,
                        categories TEXT NOT NULL,
                        pdf_url TEXT,
                        source TEXT NOT NULL,
                        keywords TEXT,
                        doi TEXT,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL
                    )
                """)
                
                # 创建摘要表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS summaries (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        paper_id TEXT NOT NULL,
                        summary TEXT NOT NULL,
                        key_points TEXT,
                        methodology TEXT,
                        significance TEXT,
                        limitations TEXT,
                        generated_at TEXT NOT NULL,
                        model_used TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (paper_id) REFERENCES papers (id)
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_papers_source ON papers (source)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_papers_published_date ON papers (published_date)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_summaries_paper_id ON summaries (paper_id)")
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    def save_paper(self, paper: Paper) -> bool:
        """
        保存论文到数据库
        
        Args:
            paper: 论文对象
            
        Returns:
            是否保存成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO papers (
                        id, title, authors, abstract, published_date, updated_date,
                        categories, pdf_url, source, keywords, doi, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    paper.id,
                    paper.title,
                    json.dumps(paper.authors, ensure_ascii=False),
                    paper.abstract,
                    paper.published_date.isoformat(),
                    paper.updated_date.isoformat() if paper.updated_date else None,
                    json.dumps(paper.categories, ensure_ascii=False),
                    paper.pdf_url,
                    paper.source,
                    json.dumps(paper.keywords or [], ensure_ascii=False),
                    paper.doi,
                    now,
                    now
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存论文失败: {str(e)}")
            return False
    
    def save_papers(self, papers: List[Paper]) -> int:
        """
        批量保存论文
        
        Args:
            papers: 论文列表
            
        Returns:
            成功保存的数量
        """
        saved_count = 0
        for paper in papers:
            if self.save_paper(paper):
                saved_count += 1
        
        logger.info(f"批量保存论文完成: {saved_count}/{len(papers)}")
        return saved_count
    
    def save_summary(self, summary: PaperSummary) -> bool:
        """
        保存论文摘要
        
        Args:
            summary: 论文摘要对象
            
        Returns:
            是否保存成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                
                cursor.execute("""
                    INSERT INTO summaries (
                        paper_id, summary, key_points, methodology, significance,
                        limitations, generated_at, model_used, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    summary.paper.id,
                    summary.summary,
                    json.dumps(summary.key_points, ensure_ascii=False),
                    summary.methodology,
                    summary.significance,
                    summary.limitations,
                    summary.generated_at,
                    summary.model_used,
                    now
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存摘要失败: {str(e)}")
            return False
    
    def get_paper(self, paper_id: str) -> Optional[Paper]:
        """
        获取单篇论文
        
        Args:
            paper_id: 论文ID
            
        Returns:
            论文对象或None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM papers WHERE id = ?", (paper_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_paper(row)
                
                return None
                
        except Exception as e:
            logger.error(f"获取论文失败: {str(e)}")
            return None
    
    def get_papers(self, source: Optional[str] = None, limit: int = 100, offset: int = 0) -> List[Paper]:
        """
        获取论文列表
        
        Args:
            source: 论文来源过滤
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            论文列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if source:
                    cursor.execute("""
                        SELECT * FROM papers WHERE source = ? 
                        ORDER BY published_date DESC LIMIT ? OFFSET ?
                    """, (source, limit, offset))
                else:
                    cursor.execute("""
                        SELECT * FROM papers 
                        ORDER BY published_date DESC LIMIT ? OFFSET ?
                    """, (limit, offset))
                
                rows = cursor.fetchall()
                return [self._row_to_paper(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取论文列表失败: {str(e)}")
            return []
    
    def get_recent_papers(self, days: int = 7) -> List[Paper]:
        """
        获取最近的论文
        
        Args:
            days: 最近几天
            
        Returns:
            论文列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                
                cursor.execute("""
                    SELECT * FROM papers 
                    WHERE published_date >= ? 
                    ORDER BY published_date DESC
                """, (cutoff_date,))
                
                rows = cursor.fetchall()
                return [self._row_to_paper(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取最近论文失败: {str(e)}")
            return []
    
    def paper_exists(self, paper_id: str) -> bool:
        """
        检查论文是否已存在
        
        Args:
            paper_id: 论文ID
            
        Returns:
            是否存在
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT 1 FROM papers WHERE id = ?", (paper_id,))
                return cursor.fetchone() is not None
                
        except Exception as e:
            logger.error(f"检查论文存在性失败: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总论文数
                cursor.execute("SELECT COUNT(*) FROM papers")
                total_papers = cursor.fetchone()[0]
                
                # 总摘要数
                cursor.execute("SELECT COUNT(*) FROM summaries")
                total_summaries = cursor.fetchone()[0]
                
                # 按来源统计
                cursor.execute("SELECT source, COUNT(*) FROM papers GROUP BY source")
                sources = dict(cursor.fetchall())
                
                # 最新论文日期
                cursor.execute("SELECT MAX(published_date) FROM papers")
                latest_date = cursor.fetchone()[0]
                
                return {
                    'total_papers': total_papers,
                    'total_summaries': total_summaries,
                    'sources': sources,
                    'latest_date': latest_date
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}
    
    def _row_to_paper(self, row) -> Paper:
        """将数据库行转换为Paper对象"""
        from src.crawler.paper_crawler import Paper

        return Paper(
            id=row[0],
            title=row[1],
            authors=json.loads(row[2]),
            abstract=row[3],
            published_date=datetime.fromisoformat(row[4]),
            updated_date=datetime.fromisoformat(row[5]) if row[5] else None,
            categories=json.loads(row[6]),
            pdf_url=row[7],
            source=row[8],
            keywords=json.loads(row[9]) if row[9] else None,
            doi=row[10]
        )
