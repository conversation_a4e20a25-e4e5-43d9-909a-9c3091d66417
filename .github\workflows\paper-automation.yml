name: 论文自动化处理

on:
  # 定时触发 - 每天北京时间9点执行
  schedule:
    - cron: '0 1 * * *'  # UTC时间1点 = 北京时间9点
  
  # 手动触发
  workflow_dispatch:
    inputs:
      test_mode:
        description: '是否运行测试模式'
        required: false
        default: 'false'
        type: boolean
      
      module:
        description: '指定运行的模块'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - crawler
          - summarizer
          - renderer
          - mailer

  # 推送到main分支时触发（用于测试）
  push:
    branches: [ main ]
    paths:
      - 'src/**'
      - 'config/**'
      - '.github/workflows/**'

env:
  PYTHON_VERSION: '3.9'

jobs:
  paper-automation:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: 创建配置文件
      run: |
        mkdir -p config logs data
        cp config/config.example.yaml config/config.yaml
    
    - name: 配置环境变量
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        EMAIL_PASSWORD: ${{ secrets.EMAIL_PASSWORD }}
        SMTP_SERVER: ${{ secrets.SMTP_SERVER }}
        SMTP_USERNAME: ${{ secrets.SMTP_USERNAME }}
      run: |
        echo "环境变量已设置"
    
    - name: 运行测试模式
      if: ${{ github.event.inputs.test_mode == 'true' || github.event_name == 'push' }}
      run: |
        python src/main.py --test
    
    - name: 运行指定模块
      if: ${{ github.event.inputs.module != 'all' && github.event.inputs.module != '' }}
      run: |
        python src/main.py --module ${{ github.event.inputs.module }}
    
    - name: 运行完整流水线
      if: ${{ github.event.inputs.module == 'all' || github.event.inputs.module == '' }}
      run: |
        python src/main.py
    
    - name: 上传输出文件
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: paper-automation-output
        path: |
          output/
          logs/
        retention-days: 30
    
    - name: 部署到GitHub Pages
      if: ${{ success() && (github.event.inputs.module == 'all' || github.event.inputs.module == '' || github.event.inputs.module == 'renderer') }}
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./output
        publish_branch: gh-pages
        force_orphan: true
        user_name: 'github-actions[bot]'
        user_email: 'github-actions[bot]@users.noreply.github.com'
        commit_message: '🤖 自动更新论文摘要网站'
    
    - name: 发送通知
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          const title = '论文自动化处理失败';
          const body = `
          工作流执行失败！
          
          **运行信息:**
          - 触发事件: ${{ github.event_name }}
          - 分支: ${{ github.ref }}
          - 提交: ${{ github.sha }}
          - 运行ID: ${{ github.run_id }}
          
          **错误详情:**
          请查看 [工作流日志](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) 获取详细信息。
          `;
          
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['bug', 'automation']
          });

  # 健康检查任务
  health-check:
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'schedule' }}
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install requests pyyaml
    
    - name: 检查网站状态
      run: |
        python -c "
        import requests
        import yaml
        
        # 读取配置
        with open('config/config.example.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        site_url = config.get('renderer', {}).get('site', {}).get('url', '')
        
        if site_url:
            try:
                response = requests.get(site_url, timeout=10)
                if response.status_code == 200:
                    print(f'✅ 网站正常访问: {site_url}')
                else:
                    print(f'❌ 网站访问异常: {site_url} (状态码: {response.status_code})')
                    exit(1)
            except Exception as e:
                print(f'❌ 网站访问失败: {str(e)}')
                exit(1)
        else:
            print('⚠️ 未配置网站URL，跳过健康检查')
        "
    
    - name: 检查依赖更新
      run: |
        pip list --outdated --format=json > outdated_packages.json
        python -c "
        import json
        
        with open('outdated_packages.json', 'r') as f:
            outdated = json.load(f)
        
        if outdated:
            print('📦 发现可更新的依赖包:')
            for pkg in outdated:
                print(f'  - {pkg[\"name\"]}: {pkg[\"version\"]} -> {pkg[\"latest_version\"]}')
        else:
            print('✅ 所有依赖包都是最新版本')
        "
