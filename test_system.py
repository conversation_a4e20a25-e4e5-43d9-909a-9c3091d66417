#!/usr/bin/env python3
"""
系统功能测试脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.config import ConfigManager
from src.utils.logger import setup_logger
from src.utils.database import PaperDatabase
from src.crawler.paper_crawler import PaperCrawler
from src.summarizer.ai_summarizer import AISummarizer


async def test_arxiv_crawler():
    """测试arXiv爬虫功能"""
    print("🔍 测试arXiv爬虫功能...")
    
    try:
        config = ConfigManager("config/config.yaml")
        crawler = PaperCrawler(config)
        
        # 测试连接
        test_papers = await crawler.test_connection()
        print(f"✅ arXiv连接测试成功，获取到 {len(test_papers)} 篇测试论文")
        
        if test_papers:
            paper = test_papers[0]
            print(f"📄 示例论文:")
            print(f"   标题: {paper.title}")
            print(f"   作者: {', '.join(paper.authors[:3])}{'...' if len(paper.authors) > 3 else ''}")
            print(f"   分类: {', '.join(paper.categories)}")
            print(f"   摘要: {paper.abstract[:100]}...")
            print(f"   发布日期: {paper.published_date.strftime('%Y-%m-%d')}")
        
        return True
        
    except Exception as e:
        print(f"❌ arXiv爬虫测试失败: {str(e)}")
        return False


async def test_ai_summarizer():
    """测试AI摘要生成功能"""
    print("\n🤖 测试AI摘要生成功能...")
    
    try:
        config = ConfigManager("config/config.yaml")
        summarizer = AISummarizer(config)
        
        # 检查API配置
        openai_key = config.get('summarizer.openai.api_key')
        claude_key = config.get('summarizer.anthropic.api_key')
        
        if not openai_key and not claude_key:
            print("⚠️ 未配置AI API密钥，跳过摘要测试")
            print("请在config/config.yaml中配置OPENAI_API_KEY或ANTHROPIC_API_KEY")
            return False
        
        # 测试API连接
        api_test = await summarizer.test_api()
        if api_test:
            print("✅ AI API连接测试成功")
        else:
            print("❌ AI API连接测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ AI摘要测试失败: {str(e)}")
        return False


async def test_database():
    """测试数据库功能"""
    print("\n💾 测试数据库功能...")
    
    try:
        # 创建测试数据库
        db = PaperDatabase("data/test_papers.db")
        
        # 创建测试论文
        from datetime import datetime
        from src.crawler.paper_crawler import Paper
        
        test_paper = Paper(
            id="test-001",
            title="测试论文标题",
            authors=["测试作者1", "测试作者2"],
            abstract="这是一个测试论文的摘要内容，用于验证数据库存储功能。",
            published_date=datetime.now(),
            updated_date=None,
            categories=["cs.AI", "cs.LG"],
            pdf_url="https://example.com/test.pdf",
            source="test"
        )
        
        # 测试保存
        success = db.save_paper(test_paper)
        if success:
            print("✅ 论文保存测试成功")
        else:
            print("❌ 论文保存测试失败")
            return False
        
        # 测试读取
        retrieved_paper = db.get_paper("test-001")
        if retrieved_paper and retrieved_paper.title == test_paper.title:
            print("✅ 论文读取测试成功")
        else:
            print("❌ 论文读取测试失败")
            return False
        
        # 测试统计
        stats = db.get_statistics()
        print(f"📊 数据库统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")
        return False


async def test_full_pipeline():
    """测试完整流水线"""
    print("\n🔄 测试完整流水线...")
    
    try:
        config = ConfigManager("config/config.yaml")
        
        # 检查必要配置
        openai_key = config.get('summarizer.openai.api_key')
        claude_key = config.get('summarizer.anthropic.api_key')
        
        if not openai_key and not claude_key:
            print("⚠️ 未配置AI API密钥，跳过完整流水线测试")
            return False
        
        # 初始化组件
        crawler = PaperCrawler(config)
        summarizer = AISummarizer(config)
        db = PaperDatabase("data/test_pipeline.db")
        
        # 1. 爬取少量论文
        print("📡 爬取论文...")
        papers = await crawler.test_connection()  # 获取少量测试论文
        
        if not papers:
            print("❌ 没有爬取到论文")
            return False
        
        print(f"✅ 爬取到 {len(papers)} 篇论文")
        
        # 2. 保存到数据库
        print("💾 保存论文...")
        saved_count = db.save_papers(papers)
        print(f"✅ 保存了 {saved_count} 篇论文")
        
        # 3. 生成摘要（只处理第一篇）
        print("🤖 生成摘要...")
        test_paper = papers[0]
        summaries = await summarizer.generate_summaries([test_paper])
        
        if summaries:
            summary = summaries[0]
            print(f"✅ 摘要生成成功")
            print(f"   摘要: {summary.summary[:100]}...")
            print(f"   关键要点: {len(summary.key_points)} 个")
            print(f"   使用模型: {summary.model_used}")
            
            # 保存摘要
            db.save_summary(summary)
            print("✅ 摘要已保存到数据库")
        else:
            print("❌ 摘要生成失败")
            return False
        
        print("🎉 完整流水线测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 完整流水线测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🧪 论文自动化处理系统 - 功能测试")
    print("=" * 50)
    
    # 检查配置文件
    config_file = Path("config/config.yaml")
    if not config_file.exists():
        print("❌ 配置文件不存在: config/config.yaml")
        print("请先运行: cp config/config.example.yaml config/config.yaml")
        return
    
    # 创建必要目录
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # 运行测试
    tests = [
        ("arXiv爬虫", test_arxiv_crawler),
        ("数据库", test_database),
        ("AI摘要", test_ai_summarizer),
        ("完整流水线", test_full_pipeline),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接。")
        
        # 提供故障排除建议
        print("\n🔧 故障排除建议:")
        if not results.get("arXiv爬虫", True):
            print("- 检查网络连接是否正常")
            print("- 确认arXiv.org可以访问")
        
        if not results.get("AI摘要", True):
            print("- 检查API密钥配置是否正确")
            print("- 确认API配额是否充足")
            print("- 验证网络是否可以访问AI服务")
        
        if not results.get("数据库", True):
            print("- 检查data目录权限")
            print("- 确认SQLite可以正常工作")


if __name__ == "__main__":
    asyncio.run(main())
