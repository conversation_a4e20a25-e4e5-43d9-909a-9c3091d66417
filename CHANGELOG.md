# 更新日志

本文件记录项目的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 项目初始化和基础框架搭建
- README.md 完整项目文档，包含详细的功能介绍、使用说明和部署指南
- CHANGELOG.md 更新日志文件，建立版本管理规范
- 完整的项目目录结构，包含所有核心模块
- requirements.txt 依赖管理文件，包含所有必需的Python包
- 配置管理系统 (src/utils/config.py)，支持YAML配置文件和环境变量
- 日志系统 (src/utils/logger.py)，基于loguru的结构化日志
- 论文爬取模块 (src/crawler/)，支持arXiv API集成
- AI摘要生成模块 (src/summarizer/)，集成OpenAI和Claude API
- 网页渲染模块 (src/renderer/)，基于Jinja2的静态网站生成
- 邮件推送模块 (src/mailer/)，支持HTML邮件和SMTP配置
- GitHub Actions 工作流 (.github/workflows/)，实现完全自动化
- 响应式网页模板系统，包含首页、详情页、分类页
- RSS订阅和Sitemap生成功能
- 完整的配置文件模板 (config/config.example.yaml)
- 主程序入口 (src/main.py)，支持命令行参数和模块化运行
- MIT开源许可证和.gitignore配置
- 自动化安装脚本 (setup.py)，简化项目初始化流程
- 开发者文档 (docs/DEVELOPMENT.md)，包含详细的开发指南
- 数据库存储模块 (src/utils/database.py)，基于SQLite的论文和摘要存储
- 系统功能测试脚本 (test_system.py)，全面验证各模块功能
- 测试配置文件 (config/config.test.yaml)，用于快速测试和开发
- 快速启动脚本 (run.py)，简化系统运行和测试流程
- 使用示例 (examples/basic_usage.py)，演示各模块的基本用法

### 功能增强
- arXiv爬虫增强：改进错误处理、数据验证和API调用稳定性
- OpenAI API集成：更新为最新的API格式，支持gpt-3.5-turbo和gpt-4
- DeepSeek API集成：完整的DeepSeek Reasoner模型支持，专门优化的推理分析
- Claude API集成：完整的Anthropic Claude API支持，包含异步处理
- 数据持久化：论文和摘要数据的SQLite存储，支持增量更新
- 主流水线优化：集成数据库存储，改进错误处理和日志记录

### 技术特性
- 模块化架构设计，各功能独立开发和测试
- 异步编程支持，提高爬取和API调用效率
- 配置化设计，支持多种数据源和AI服务
- 错误处理和重试机制
- 日志记录和监控功能
- 响应式网页设计，支持移动端访问
- SEO优化，包含Open Graph和Twitter Card支持

### 已完成的开发任务
- [x] 项目结构初始化
- [x] README.md 编写
- [x] CHANGELOG.md 创建
- [x] 基础配置文件设置
- [x] 依赖管理配置
- [x] 论文爬取模块框架
- [x] AI摘要生成模块框架
- [x] 网页渲染模块框架
- [x] 邮件推送模块框架
- [x] GitHub Actions 工作流配置
- [x] 网页模板系统设计
- [x] 配置管理和日志系统

## [0.1.0] - 2024-01-XX

### 新增
- 项目初始版本
- 基础项目结构
- 核心模块框架设计

---

## 更新说明

每次提交代码前，请务必更新此文件，详细记录：
1. **新增功能** - 新添加的功能特性
2. **更改** - 对现有功能的修改
3. **弃用** - 即将移除的功能
4. **移除** - 已删除的功能
5. **修复** - 错误修复
6. **安全** - 安全相关的更改

### 版本号规则
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 提交规范
- 每次提交前必须更新 CHANGELOG.md
- Git 提交信息可以简洁，但 CHANGELOG 要详细
- 包含具体的文件更改和功能描述
- 注明影响范围和使用注意事项
